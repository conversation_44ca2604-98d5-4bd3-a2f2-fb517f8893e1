#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
对tests/top2000.csv进行画质优化测试的脚本
使用maya client调用服务，支持并发处理和进度条显示
"""

import json
import csv
import time
import random
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import pandas as pd

# MayaClient imports
try:
    from rserving_client.rserving_client import (
        ArksConfig,
        ArksRequest,
        MayaClient
    )
    MAYA_CLIENT_AVAILABLE = True
except ImportError:
    print("MayaClient not available, falling back to POST requests")
    MAYA_CLIENT_AVAILABLE = False
    import requests

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("CoverEnhanceTest")


class SmartCoverEnhancer:
    """智能封面增强调用器"""
    
    def __init__(self, use_maya=True):
        self.use_maya = use_maya and MAYA_CLIENT_AVAILABLE
        self.scene_name = "smart_cover_enhancement"
        self.post_url = "https://paiplusinference.alipay.com/inference/7a2f7401cee56342_smart_cover_enhancement/v1"
        self.input_key = "data"
        self.output_key = "output"
        
        if self.use_maya:
            self.client = MayaClient.get_instance()
            logger.info("Using MayaClient for API calls")
        else:
            logger.info("Using POST requests for API calls")
    
    def _call_maya(self, data, timeout=30000):
        """使用MayaClient调用服务"""
        try:
            request = ArksRequest()
            request.request_timeout = timeout
            request.read_timeout = timeout
            request.connect_timeout = timeout
            request.scene_name = self.scene_name
            request.chain_name = "v1"
            request.session_id = f"test-{int(time.time())}-{random.randint(1000, 9999)}"
            request.set_lbconfig({"arks.antvip.arrow.cross.city": "true"})
            request.set_item_num(1)

            item = request.add_item("item-1")
            item.add_tensor_feature(self.input_key, [1], [json.dumps(data)])

            res = self.client.call(request)
            if res.success:
                attrs = res.items[0].attributes
                if self.output_key not in attrs:
                    return {"error": f"No {self.output_key} in response"}
                
                # 解析输出结果
                output_str = attrs[self.output_key]
                try:
                    return json.loads(output_str)
                except json.JSONDecodeError:
                    return {"error": f"Failed to parse output JSON: {output_str}"}
            else:
                logger.error(f"MayaClient call failed: {res.error_msg}")
                return {"error": res.error_msg}
        except Exception as e:
            logger.error(f"MayaClient exception: {e}")
            return {"error": str(e)}
    
    def _call_post(self, data, timeout=30):
        """使用POST请求调用服务"""
        try:
            body = {
                "features": {},
                "tensorFeatures": {
                    self.input_key: {
                        "shapes": [1],
                        "stringValues": [json.dumps(data)]
                    }
                }
            }
            
            headers = {
                "Content-Type": "application/json;charset=utf-8",
                "MPS-app-name": "your-app-name",
                "MPS-http-version": "1.0",
                "MPS-trace-id": f"test-{int(time.time())}-{random.randint(1000, 9999)}"
            }
            
            response = requests.post(url=self.post_url, json=body, headers=headers, timeout=timeout)
            res = response.json()
            
            if res.get("success") and "resultMap" in res and self.output_key in res["resultMap"]:
                output_str = res["resultMap"][self.output_key]
                try:
                    return json.loads(output_str)
                except json.JSONDecodeError:
                    return {"error": f"Failed to parse output JSON: {output_str}"}
            else:
                logger.error(f"POST call failed: {res}")
                return {"error": res.get("errorMessage", "Unknown error")}
        except Exception as e:
            logger.error(f"POST request exception: {e}")
            return {"error": str(e)}
    
    def enhance_cover(self, smart_cover_id, title, cat, content_id, timeout=30000):
        """调用智能封面增强服务"""
        input_data = {
            "cover_id": smart_cover_id,
            "cover_url": "",
            "title": title,
            "cat": cat,
            "video_name": "",
            "video_id": "",
            "contentId": content_id,
            "logs": {},
            "version": 1
        }
        
        if self.use_maya:
            return self._call_maya(input_data, timeout)
        else:
            return self._call_post(input_data, timeout // 1000)  # POST timeout in seconds


def process_single_row(enhancer, row_data):
    """处理单行数据"""
    content_id = row_data['ctt_id']
    title = row_data['ctt_title']
    cat = row_data['cate4_level1_name']
    smart_cover_id = row_data['smart_cover_id']
    
    try:
        # 调用增强服务
        result = enhancer.enhance_cover(smart_cover_id, title, cat, content_id)
        
        # 构造输出行
        output_row = {
            'contentid': content_id,
            'aftsid': smart_cover_id,  # 默认使用原始smart_cover_id
            'subVersion': 1,
            'bizType': 'home_page_aigc',
            'type': 'cover'
        }
        
        # 如果增强成功，使用新的cover_id
        if 'new_cover_id' in result and result['new_cover_id']:
            output_row['aftsid'] = result['new_cover_id']
        
        return output_row, None
        
    except Exception as e:
        error_msg = f"Error processing {content_id}: {str(e)}"
        logger.error(error_msg)
        return None, error_msg


def main():
    """主函数"""
    # 读取CSV文件
    input_file = "tests/top2000.csv"
    output_file = "tests/top2000_enhanced_results.csv"
    
    logger.info(f"Reading input file: {input_file}")
    
    try:
        df = pd.read_csv(input_file)
        logger.info(f"Loaded {len(df)} rows from CSV")
    except Exception as e:
        logger.error(f"Failed to read CSV file: {e}")
        return
    
    # 初始化增强器
    enhancer = SmartCoverEnhancer(use_maya=True)
    
    # 准备结果列表
    results = []
    errors = []
    
    # 并发处理配置
    max_workers = 10  # 可以根据需要调整并发数
    
    logger.info(f"Starting processing with {max_workers} workers...")
    
    # 使用ThreadPoolExecutor进行并发处理
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_row = {
            executor.submit(process_single_row, enhancer, row.to_dict()): idx 
            for idx, row in df.iterrows()
        }
        
        # 使用tqdm显示进度条
        with tqdm(total=len(future_to_row), desc="Processing covers") as pbar:
            for future in as_completed(future_to_row):
                row_idx = future_to_row[future]
                try:
                    result, error = future.result()
                    if result:
                        results.append(result)
                    if error:
                        errors.append(f"Row {row_idx}: {error}")
                except Exception as e:
                    error_msg = f"Row {row_idx}: Exception in future: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
                
                pbar.update(1)
    
    # 保存结果
    if results:
        result_df = pd.DataFrame(results)
        result_df.to_csv(output_file, index=False)
        logger.info(f"Saved {len(results)} results to {output_file}")
    else:
        logger.warning("No results to save")
    
    # 报告错误
    if errors:
        error_file = "tests/top2000_enhancement_errors.log"
        with open(error_file, 'w', encoding='utf-8') as f:
            for error in errors:
                f.write(f"{error}\n")
        logger.warning(f"Encountered {len(errors)} errors, saved to {error_file}")
    
    logger.info("Processing completed!")
    print(f"\nSummary:")
    print(f"- Total rows processed: {len(df)}")
    print(f"- Successful results: {len(results)}")
    print(f"- Errors: {len(errors)}")
    print(f"- Output file: {output_file}")
    if errors:
        print(f"- Error log: tests/top2000_enhancement_errors.log")


if __name__ == "__main__":
    main()
